# MaoConsult Design System & Visual Specifications - Updated

## Typography System

### Font Family
**Primary Font**: Urbanist
**Fallback**: "Urbanist", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif

### Text Hierarchy (Simplified)

#### Heading 1 (Hero Headlines)
- **Font**: Urbanist
- **Weight**: 700 (Bold)
- **Size**: 56px (desktop), 40px (mobile)
- **Line Height**: 1.1
- **Color**: #FFFFFF (on hero eagle background)
- **Usage**: Hero headlines only

#### Heading 2 (Section Headlines)
- **Font**: Urbanist
- **Weight**: 600 (Semi-Bold)
- **Size**: 40px (desktop), 32px (mobile)
- **Line Height**: 1.2
- **Color**: #000000
- **Usage**: Major section headings

#### Heading 3 (Card/Component Headlines)
- **Font**: Urbanist
- **Weight**: 600 (Semi-Bold)
- **Size**: 24px
- **Line Height**: 1.3
- **Color**: #000000
- **Usage**: Card titles, form steps

#### Body Large (Hero Subtext)
- **Font**: Urbanist
- **Weight**: 400 (Regular)
- **Size**: 20px
- **Line Height**: 1.5
- **Color**: #FFFFFF (on hero), #333333 (elsewhere)
- **Usage**: Hero descriptions, important copy

#### Body Regular
- **Font**: Urbanist
- **Weight**: 400 (Regular)
- **Size**: 16px
- **Line Height**: 1.6
- **Color**: #333333
- **Usage**: Main content, form labels

#### Button Text
- **Font**: Urbanist
- **Weight**: 500 (Medium)
- **Size**: 16px
- **Line Height**: 1.4
- **Color**: #FFFFFF (primary buttons), #f7642f (secondary buttons)

---

## Color System (Simplified)

### Primary Colors
- **Accent Orange**: #f7642f (buttons, links, active states)
- **Pure White**: #FFFFFF (cards, forms, overlays)
- **Text Dark**: #000000 (headings)
- **Text Body**: #333333 (body text)
- **Border Light**: #e5e7eb (subtle borders)

### Hero Section Colors
- **Background**: Eagle image with dark overlay (40% opacity)
- **Text**: #FFFFFF for all hero text
- **Buttons**: Primary (#f7642f), Secondary (transparent with white border)

### Page Background
- **Main Background**: #f8f9fa (light neutral for card contrast)

---

## Layout System

### Container & Spacing
- **Max Content Width**: 1200px
- **Container Padding**: 24px (mobile), 40px (tablet), 80px (desktop)
- **Section Spacing**: 120px between major sections
- **Card Padding**: 40px internal padding
- **Element Spacing Scale**: 8px, 16px, 24px, 32px, 48px, 64px

### Modern Landing Page Grid
- **Hero Section**: Full-width eagle background, centered content (max 800px width)
- **Overlapping Form**: 800px width (2/3 of 1200px), positioned between hero and services
- **Services Section**: 2-column grid with 32px gap
- **Other Sections**: Single column, max 800px width for readability

---

## Header Design (Updated)

### Transparent Header
- **Background**: Transparent (always, no scroll changes)
- **Position**: Fixed, z-index: 1000
- **Height**: 80px
- **No borders, shadows, or background colors**
- **Logo**: "MaoConsult" - #FFFFFF on hero, #000000 on scroll
- **Navigation**: #FFFFFF on hero, #333333 on scroll
- **Smooth transitions**: 0.3s ease for color changes

### Header Content
- **Logo Size**: 24px, weight 600
- **Nav Links**: 16px, weight 400, 32px spacing between items
- **CTA Button**: Primary button style with hover effects

---

## Hero Section Design

### Background Treatment
- **Eagle Image**: Full-width background image (provided URL)
- **Overlay**: Dark gradient overlay (rgba(0,0,0,0.4)) for text readability
- **Height**: 100vh minimum, content-driven maximum
- **Background Position**: center center, background-size: cover

### Hero Content Layout
- **Content Width**: Max 800px, centered
- **Vertical Alignment**: Center
- **Text Color**: All white (#FFFFFF)
- **Button Layout**: Primary and secondary side-by-side (mobile: stacked)

### Hero Buttons
- **Primary**: #f7642f background, white text, 16px padding vertical, 32px horizontal
- **Secondary**: Transparent background, white border (2px), white text
- **Hover States**: Primary (opacity 0.9), Secondary (white background, dark text)

---

## Overlapping Conversational Form

### Form Positioning
- **Width**: 800px (2/3 of 1200px container)
- **Position**: Overlaps hero bottom and services top by 80px each direction
- **Z-index**: 100 (above both sections)
- **Centering**: Horizontally centered on page

### Form Card Design
- **Background**: #FFFFFF
- **Border Radius**: 24px (modern, rounded)
- **Padding**: 48px
- **Shadow**: 0 20px 40px rgba(0,0,0,0.1) - soft, modern elevation
- **Border**: None

### Conversational Form Style
- **Progress Indicator**: Horizontal progress bar (#f7642f) at top
- **Question Layout**: One question per screen with smooth transitions
- **Typography**: 
  - Questions: 24px, weight 600, #000000
  - Options: 16px, weight 400, #333333
  - Helper text: 14px, weight 400, #666666

### Form Interactions
- **Option Buttons**: 
  - Default: White background, #e5e7eb border, 16px padding
  - Selected: #f7642f background, white text
  - Hover: Subtle scale (1.02x)
- **Navigation**: 
  - "Continue" button: Primary style
  - "Back" button: Text link in #f7642f
- **Animations**: Slide transitions between steps (0.3s ease)

---

## Card Design System

### Service Cards
- **Background**: #FFFFFF
- **Border Radius**: 16px
- **Padding**: 40px
- **Border**: None
- **Hover**: Subtle lift (transform: translateY(-4px)), shadow increase
- **Icons**: 48px size, #f7642f color
- **Spacing**: 32px gap between cards

### Package Cards
- **Background**: #FFFFFF
- **Border Radius**: 16px
- **Padding**: 32px
- **Popular Badge**: #f7642f background, white text, top-right positioning
- **Pricing**: Large text (32px), weight 700, #000000
- **Features**: Bulleted list with #22c55e checkmarks

---

## Button System

### Primary Buttons
- **Background**: #f7642f
- **Text**: #FFFFFF, 16px, weight 500
- **Padding**: 16px vertical, 32px horizontal
- **Border Radius**: 12px
- **Hover**: opacity 0.9, subtle scale (1.02x)
- **Active**: Slightly darker shade (#e55a2b)

### Secondary Buttons
- **Background**: Transparent
- **Border**: 2px solid #f7642f
- **Text**: #f7642f, 16px, weight 500
- **Same dimensions as primary**
- **Hover**: #f7642f background, white text

### Text Links
- **Color**: #f7642f
- **Hover**: Underline, slight opacity change
- **Weight**: 500 for emphasis links, 400 for regular

---

## Footer Design (Simplified)

### Footer Layout
- **Background**: #f8f9fa (same as page background)
- **Padding**: 48px vertical, container padding horizontal
- **No borders or dividers**
- **Content**: Single row, centered alignment

### Footer Content
- **Copyright**: 14px, #666666, center alignment
- **Social Links**: Icon-only, 24px size, #333333 default, #f7642f hover
- **Minimal navigation**: 3-4 key links max, 14px, #666666

---

## Responsive Breakpoints

### Mobile (≤768px)
- **Container Padding**: 24px
- **Form Width**: 90% of screen width
- **Hero Text**: Reduce by 30%
- **Section Spacing**: 80px
- **Card Padding**: 24px

### Tablet (769px - 1024px)
- **Container Padding**: 40px
- **Form Width**: 600px
- **Maintain 2-column layouts where possible

### Desktop (1025px+)
- **All specifications as designed**
- **Container max-width**: 1200px
- **Form width**: 800px

---

## Animation & Micro-interactions

### Page Transitions
- **Smooth scrolling**: CSS scroll-behavior: smooth
- **Section reveals**: Fade-in on scroll (using Intersection Observer)
- **Form transitions**: Slide animations between steps

### Hover States
- **Cards**: translateY(-4px) + shadow increase
- **Buttons**: opacity + scale changes
- **Links**: Color transition (0.2s ease)

### Loading States
- **Form submission**: Button shows spinner, text changes to "Processing..."
- **Image loading**: Subtle pulse animation placeholder

---

## Accessibility Standards

### Color Contrast
- **Text on backgrounds**: Minimum WCAG AA compliance
- **Interactive elements**: Clear focus indicators with #f7642f
- **Hero text on eagle image**: Ensure overlay provides sufficient contrast

### Interactive Elements
- **Keyboard navigation**: Clear tab order and focus states
- **Screen readers**: Proper ARIA labels and semantic markup
- **Touch targets**: Minimum 44px height for mobile interactions

---

## Professional Standards & Guidelines

### Icon Usage (STRICT)
- **NO EMOJIS ANYWHERE** on the website - maintain professional aesthetic
- **Use proper SVG icons** from libraries like Lucide React, Heroicons, or Feather
- **Icon sizing**: 24px standard, 48px for feature icons, 16px for inline icons
- **Icon color**: #f7642f for accent icons, #333333 for neutral icons
- **Consistent style**: Use same icon library throughout (recommend Lucide React)

---

## React + Vite + TypeScript Best Practices

### Component Architecture DO's
- **Create reusable components** for buttons, cards, form inputs, and icons
- **Use TypeScript interfaces** for all props and data structures
- **Implement compound components** for complex UI (e.g., Form.Step, Card.Header)
- **Use CSS modules or styled-components** for component-scoped styling
- **Create a design tokens file** for colors, spacing, and typography values

### Performance DO's
- **Lazy load sections** below the fold using React.lazy()
- **Optimize images** - use WebP format with fallbacks, implement lazy loading
- **Code splitting** by route/section for better load times
- **Use React.memo** for expensive components that re-render frequently
- **Implement proper TypeScript** - no 'any' types, strict mode enabled

### State Management DO's
- **Use React Hook Form** for the conversational form (better performance)
- **Implement proper form validation** with TypeScript schemas (Zod recommended)
- **Use context sparingly** - only for truly global state
- **Local state first** - useReducer for complex component state

### Animation & UX DO's
- **Use Framer Motion** for smooth page transitions and micro-interactions
- **Implement intersection observer** for scroll-triggered animations
- **60fps animations** - transform and opacity changes only
- **Respect reduced motion** preferences with CSS media queries
- **Loading states** for all async operations (form submission, image loading)

---

## Development DON'Ts (CRITICAL)

### Performance DON'Ts
- **DON'T use inline styles** - impacts performance and maintainability
- **DON'T import entire icon libraries** - use tree shaking or individual imports
- **DON'T use heavy animation libraries** - Framer Motion is sufficient
- **DON'T put large images in src/assets** - use public folder or CDN
- **DON'T forget to optimize bundle size** - analyze with Bundle Analyzer

### Code Quality DON'Ts
- **DON'T use CSS-in-JS for static styles** - prefer CSS modules for better performance
- **DON'T ignore TypeScript errors** - fix all type issues before deployment
- **DON'T create mega-components** - break down into smaller, focused components
- **DON'T use useEffect for everything** - prefer derived state where possible
- **DON'T ignore accessibility** - proper ARIA labels and keyboard navigation

### UX/UI DON'Ts
- **DON'T use auto-playing videos** - impacts performance and UX
- **DON'T ignore mobile-first design** - design mobile first, then scale up
- **DON'T use non-standard form patterns** - stick to familiar UX patterns
- **DON'T overuse animations** - subtle is better than flashy
- **DON'T ignore loading states** - every async action needs feedback

---

## File Structure Recommendations

```
src/
  components/
    ui/            # Reusable UI components (Button, Input, Card)
    sections/      # Page sections (Hero, Services, Form)
    icons/         # Custom icon components
  hooks/           # Custom React hooks
  types/           # TypeScript type definitions
  styles/          # Global styles and design tokens
  utils/           # Helper functions and constants
```

---

## Technical Implementation Notes

### Form Implementation
- **React Hook Form + Zod** for validation
- **Multi-step form** using state machine pattern
- **Progressive enhancement** - works without JavaScript
- **Proper form accessibility** with labels and error messages

### Styling Strategy
- **CSS Custom Properties** for design tokens (colors, spacing)
- **CSS Modules** for component styling
- **Global styles** only for resets and typography
- **Responsive design** using container queries where supported

### SEO & Meta Tags
- **React Helmet Async** for dynamic meta tags
- **Structured data** for business information
- **Open Graph tags** for social sharing
- **Proper heading hierarchy** for accessibility and SEO

This design system creates a modern, conversion-focused landing page that balances visual impact with usability while maintaining professional standards and optimal React development practices.