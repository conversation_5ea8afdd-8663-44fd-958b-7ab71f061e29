#!/bin/bash

echo "🧪 Testing Mao Website Setup (Netlify-ready)"
echo "=============================================="

echo ""
echo "1. Checking if Cloudflare dependencies are removed..."
if grep -q "cloudflare\|wrangler" package.json; then
    echo "❌ Cloudflare dependencies still found in package.json"
    exit 1
else
    echo "✅ Cloudflare dependencies removed from package.json"
fi

echo ""
echo "2. Checking if Cloudflare config files are removed..."
if [ -f "wrangler.jsonc" ] || [ -f "tsconfig.worker.json" ] || [ -d "src/worker" ]; then
    echo "❌ Cloudflare config files still exist"
    exit 1
else
    echo "✅ Cloudflare config files removed"
fi

echo ""
echo "3. Checking if Netlify config exists..."
if [ -f "netlify.toml" ]; then
    echo "✅ netlify.toml configuration file exists"
else
    echo "❌ netlify.toml configuration file missing"
    exit 1
fi

echo ""
echo "4. Testing TypeScript compilation..."
if npx tsc --noEmit; then
    echo "✅ TypeScript compilation successful"
else
    echo "❌ TypeScript compilation failed"
    exit 1
fi

echo ""
echo "5. Testing build process..."
if npm run build; then
    echo "✅ Build process successful"
else
    echo "❌ Build process failed"
    exit 1
fi

echo ""
echo "6. Checking if build output exists..."
if [ -d "dist" ]; then
    echo "✅ Build output directory (dist) exists"
    echo "   Files in dist:"
    ls -la dist/ | head -10
else
    echo "❌ Build output directory (dist) not found"
    exit 1
fi

echo ""
echo "🎉 All tests passed! Your project is ready for Netlify deployment."
echo ""
echo "Next steps:"
echo "1. Push your code to a Git repository"
echo "2. Connect your repository to Netlify"
echo "3. Netlify will automatically use the settings in netlify.toml"
echo "4. Your site will be deployed to a .netlify.app domain"
