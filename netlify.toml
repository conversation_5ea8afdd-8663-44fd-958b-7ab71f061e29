[build]
  # Directory to change to before starting a build
  base = "."
  
  # Directory that contains the deploy-ready HTML files and assets generated by the build
  publish = "dist"
  
  # Default build command
  command = "npm run build"

[build.environment]
  # Node.js version to use
  NODE_VERSION = "18"

# Redirect rules for Single Page Application (SPA)
# This ensures that all routes are handled by React Router
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache images
[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

# Cache fonts
[[headers]]
  for = "/*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Prerender settings for better SEO
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

# Form handling (if you have contact forms)
[forms]
  # Enable form processing
  # Forms will be automatically detected and processed by Netlify
