import z from "zod";

// Form validation schemas
export const ContactFormSchema = z.object({
  challenge: z.string().min(1, "Please select a challenge"),
  businessStage: z.string().min(1, "Please select your business stage"),
  timeline: z.string().min(1, "Please select a timeline"),
  businessName: z.string().min(2, "Business name is required"),
  yourName: z.string().min(2, "Your name is required"),
  whatsapp: z.string().min(10, "Valid WhatsApp number is required"),
  email: z.string().email("Valid email is required"),
});

export type ContactFormType = z.infer<typeof ContactFormSchema>;

// Form step data
export interface FormStepData {
  step: number;
  title: string;
  question: string;
  options?: Array<{
    value: string;
    label: string;
    description?: string;
  }>;
}
