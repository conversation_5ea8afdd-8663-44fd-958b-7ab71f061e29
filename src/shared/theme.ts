// Centralized Theme Configuration
// Change colors here to update the entire website

export const theme = {
  // Background colors
  colors: {
    // Primary section background - change this to update all sections
    sectionBackground: '#efebe5',
    
    // Header background (with transparency)
    headerBackground: '#efebe5dd', // 87% opacity
    
    // You can add more color variations here for future use
    // Alternative backgrounds (uncomment to switch):
    // sectionBackground: '#f8f9fa', // Light gray
    // sectionBackground: '#fff5f5', // Light pink
    // sectionBackground: '#f0fdf4', // Light green
    // sectionBackground: '#fefce8', // Light yellow
  },
  
  // CSS-in-JS style objects
  styles: {
    sectionBackground: {
      backgroundColor: '#efebe5'
    },
    
    headerScrollBackground: {
      backgroundColor: '#efebe5dd'
    }
  },
  
  // Tailwind CSS classes (for dynamic class generation)
  classes: {
    // Note: For custom colors not in Tailwind, use style objects above
    // These would be for standard Tailwind colors:
    // sectionBackground: 'bg-gray-100',
  }
} as const;

// Export individual values for convenience
export const sectionBackgroundColor = theme.colors.sectionBackground;
export const headerBackgroundColor = theme.colors.headerBackground;
export const sectionBackgroundStyle = theme.styles.sectionBackground;
export const headerScrollBackgroundStyle = theme.styles.headerScrollBackground;

// Type exports for TypeScript
export type ThemeColors = typeof theme.colors;
export type ThemeStyles = typeof theme.styles;