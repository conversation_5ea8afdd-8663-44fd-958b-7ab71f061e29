import { MapP<PERSON>, Link, TrendingUp } from 'lucide-react';
import { sectionBackgroundStyle } from '@/shared/theme';

export default function WhyChooseUs() {
  const benefits = [
    {
      icon: MapPin,
      title: "Global Market Expertise",
      description: "Deep understanding of international business environments, tax requirements, and diverse customer behavior across global markets."
    },
    {
      icon: Link,
      title: "Integrated Services", 
      description: "Marketing and accounting services work together - track ROI, understand customer acquisition costs, make data-driven decisions."
    },
    {
      icon: TrendingUp,
      title: "Predictable Results",
      description: "Fixed monthly packages with clear deliverables. No surprises, no scope creep, just consistent professional service."
    }
  ];

  return (
    <section id="about" className="py-24" style={sectionBackgroundStyle}>
      <div className="max-w-6xl mx-auto px-6 lg:px-20">
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            Why businesses choose MaoConsult
          </h2>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <div key={index} className="text-center">
              <div className="w-20 h-20 bg-orange-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <benefit.icon className="w-10 h-10 text-orange-500" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {benefit.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {benefit.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
