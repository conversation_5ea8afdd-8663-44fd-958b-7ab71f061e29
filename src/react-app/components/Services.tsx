import { Smartphone, TrendingUp } from 'lucide-react';
import { sectionBackgroundStyle } from '@/shared/theme';

export default function Services() {
  return (
    <section id="services" className="py-24" style={sectionBackgroundStyle} itemScope itemType="https://schema.org/Service">
      <div className="max-w-6xl mx-auto px-6 lg:px-20">
        <header className="text-center mb-16">
          <h2 className="text-5xl font-bold text-gray-900 mb-6" itemProp="name">
            How we help growing businesses
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto" itemProp="description">
            Two essential services that work better together
          </p>
        </header>

        <div className="grid lg:grid-cols-2 gap-8" role="region" aria-label="Our Services">
          {/* Social Media Service */}
          <article className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-gray-100" itemScope itemType="https://schema.org/Service">
            <div className="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center mb-6" role="img" aria-label="Social Media Management Icon">
              <Smartphone className="w-8 h-8 text-orange-500" />
            </div>
            
            <h3 className="text-2xl font-bold text-gray-900 mb-4" itemProp="name">
              Get More Customers Online
            </h3>
            
            <p className="text-gray-600 mb-6 leading-relaxed" itemProp="description">
              Professional social media management that actually generates leads. We create content, manage your pages, and track every lead back to sales.
            </p>
            
            <ul className="space-y-3 mb-6" role="list">
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-gray-700">Professional content that reflects your brand</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-gray-700">Systematic lead generation campaigns</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-gray-700">Performance tracking with clear ROI</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-gray-700">More time to focus on serving customers</span>
              </li>
            </ul>
            
            <div className="bg-orange-50 rounded-xl p-4">
              <p className="text-orange-800 font-medium text-sm">
                Our clients typically see 3-5 qualified leads per month within 60 days
              </p>
            </div>
          </article>

          {/* Accounting Service */}
          <article className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-gray-100" itemScope itemType="https://schema.org/Service">
            <div className="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center mb-6" role="img" aria-label="Accounting Services Icon">
              <TrendingUp className="w-8 h-8 text-orange-500" />
            </div>
            
            <h3 className="text-2xl font-bold text-gray-900 mb-4" itemProp="name">
              Get Your Books Organized
            </h3>
            
            <p className="text-gray-600 mb-6 leading-relaxed" itemProp="description">
              Professional bookkeeping and tax compliance so you always know where your business stands financially. Clean books, clear reports, stress-free tax season.
            </p>
            
            <ul className="space-y-3 mb-6" role="list">
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-gray-700">Complete monthly bookkeeping and reconciliation</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-gray-700">KRA compliance and tax preparation</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-gray-700">Clear financial reports you can actually understand</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-gray-700">Better cash flow management and planning</span>
              </li>
            </ul>
            
            <div className="bg-orange-50 rounded-xl p-4">
              <p className="text-orange-800 font-medium text-sm">
                Clients save 10+ hours monthly and reduce tax stress by 90%
              </p>
            </div>
          </article>
        </div>
      </div>
    </section>
  );
}
