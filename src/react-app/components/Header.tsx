import { useState, useEffect } from 'react';
import { headerScrollBackgroundStyle } from '@/shared/theme';

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <header className={`fixed top-0 w-full z-50 transition-all duration-300 ${
      isScrolled ? 'backdrop-blur-sm' : ''
    }`} role="banner" style={{
      backgroundColor: isScrolled ? headerScrollBackgroundStyle.backgroundColor : 'transparent'
    }}>
      <div className="max-w-6xl mx-auto px-6 lg:px-20">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <div className="text-2xl font-semibold">
            <button
              onClick={() => scrollToSection('hero')}
              className={`transition-colors duration-300 hover:text-orange-500 ${
                isScrolled ? 'text-gray-900' : 'text-white'
              }`}
            >
              MaoConsult
            </button>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8" role="navigation" aria-label="Main navigation">
            <button
              onClick={() => scrollToSection('services')}
              className={`transition-colors duration-300 hover:text-orange-500 ${
                isScrolled ? 'text-gray-700' : 'text-white'
              }`}
              aria-label="Navigate to Services section"
            >
              Services
            </button>
            <button
              onClick={() => scrollToSection('packages')}
              className={`transition-colors duration-300 hover:text-orange-500 ${
                isScrolled ? 'text-gray-700' : 'text-white'
              }`}
              aria-label="Navigate to Packages section"
            >
              Packages
            </button>
            <button
              onClick={() => scrollToSection('about')}
              className={`transition-colors duration-300 hover:text-orange-500 ${
                isScrolled ? 'text-gray-700' : 'text-white'
              }`}
              aria-label="Navigate to About section"
            >
              About
            </button>
            <button
              onClick={() => scrollToSection('contact')}
              className={`transition-colors duration-300 hover:text-orange-500 ${
                isScrolled ? 'text-gray-700' : 'text-white'
              }`}
              aria-label="Navigate to Contact section"
            >
              Contact
            </button>
          </nav>

          {/* CTA Button */}
          <button
            onClick={() => scrollToSection('form')}
            className="bg-orange-500 text-white px-8 py-3 rounded-xl font-medium hover:bg-orange-600 transition-colors duration-300"
            aria-label="Get started with free consultation"
          >
            Get Started
          </button>
        </div>
      </div>
    </header>
  );
}
