import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ContactFormSchema, ContactFormType } from '@/shared/types';
import { ChevronLeft, ChevronRight, CheckCircle } from 'lucide-react';
import { sectionBackgroundStyle } from '@/shared/theme';

const formSteps = [
  {
    step: 1,
    title: "Service Interest",
    question: "What's your biggest business challenge right now?",
    options: [
      { value: "online-customers", label: "Getting more customers online", description: "Social Media focus" },
      { value: "finances", label: "Organizing my finances", description: "Accounting focus" },
      { value: "systematic-growth", label: "Growing systematically", description: "Complete bundle" },
      { value: "not-sure", label: "I'm not sure", description: "Custom consultation" }
    ]
  },
  {
    step: 2,
    title: "Business Stage",
    question: "Tell us about your business size:",
    options: [
      { value: "starting", label: "Starting out", description: "≤KES 500K monthly revenue" },
      { value: "growing", label: "Growing steady", description: "KES 500K - 3M monthly revenue" },
      { value: "established", label: "Established", description: "KES 3M+ monthly revenue" }
    ]
  },
  {
    step: 3,
    title: "Timeline",
    question: "When do you want to see results?",
    options: [
      { value: "asap", label: "ASAP", description: "within 30 days" },
      { value: "next-quarter", label: "Next quarter", description: "60-90 days" },
      { value: "long-term", label: "Long-term growth", description: "6+ months planning" }
    ]
  }
];

export default function ConversationalForm() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { register, handleSubmit, watch, setValue, trigger, formState: { errors } } = useForm<ContactFormType>({
    resolver: zodResolver(ContactFormSchema),
    mode: 'onChange'
  });

  const watchedValues = watch();

  const onSubmit = async (data: ContactFormType) => {
    setIsSubmitting(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    console.log('Form submitted:', data);
    setIsSubmitted(true);
    setIsSubmitting(false);
  };

  const nextStep = async () => {
    const stepFields = getStepFields(currentStep);
    const isValid = await trigger(stepFields as (keyof ContactFormType)[]);
    if (isValid && currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getStepFields = (step: number) => {
    switch (step) {
      case 1: return ['challenge'];
      case 2: return ['businessStage'];
      case 3: return ['timeline'];
      case 4: return ['businessName', 'yourName', 'whatsapp', 'email'];
      default: return [];
    }
  };

  const selectOption = (field: keyof ContactFormType, value: string) => {
    setValue(field, value);
    trigger(field);
    // Auto-advance after selection for steps 1-3
    if (currentStep < 4) {
      setTimeout(() => nextStep(), 300);
    }
  };

  if (isSubmitted) {
    return (
      <section style={sectionBackgroundStyle} className="pb-20">
        <div id="form" className="relative z-30 -mt-20 mb-20">
          <div className="max-w-4xl mx-auto px-6">
            <div className="bg-white rounded-3xl p-12 shadow-2xl text-center">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-6" />
              <h3 className="text-3xl font-bold text-gray-900 mb-4">Thank You!</h3>
              <p className="text-lg text-gray-600">
                We'll send your custom package recommendation to your WhatsApp within 2 hours.
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section style={sectionBackgroundStyle} className="pb-20">
      <div id="form" className="relative z-30 -mt-20 mb-20">
        <div className="max-w-4xl mx-auto px-6">
          <div className="bg-white rounded-3xl p-8 lg:p-12 shadow-2xl">
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between text-sm text-gray-500 mb-2">
              <span>Step {currentStep} of 4</span>
              <span>{Math.round((currentStep / 4) * 100)}% complete</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-orange-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${(currentStep / 4) * 100}%` }}
              />
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Steps 1-3: Multiple Choice */}
            {currentStep <= 3 && (
              <div className="min-h-[400px]">
                <h3 className="text-3xl font-bold text-gray-900 mb-8">
                  {formSteps[currentStep - 1].question}
                </h3>
                <div className="space-y-4">
                  {formSteps[currentStep - 1].options?.map((option) => {
                    const fieldName = currentStep === 1 ? 'challenge' : currentStep === 2 ? 'businessStage' : 'timeline';
                    const isSelected = watchedValues[fieldName as keyof ContactFormType] === option.value;
                    
                    return (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => selectOption(fieldName as keyof ContactFormType, option.value)}
                        className={`w-full p-6 rounded-xl border-2 text-left transition-all duration-200 hover:scale-[1.02] ${
                          isSelected 
                            ? 'border-orange-500 bg-orange-50' 
                            : 'border-gray-200 hover:border-orange-300'
                        }`}
                      >
                        <div className="text-lg font-semibold text-gray-900">
                          {option.label}
                        </div>
                        {option.description && (
                          <div className="text-gray-600 mt-1">
                            {option.description}
                          </div>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Step 4: Contact Information */}
            {currentStep === 4 && (
              <div className="min-h-[400px]">
                <h3 className="text-3xl font-bold text-gray-900 mb-2">
                  Perfect! Let's get you a custom recommendation:
                </h3>
                <p className="text-gray-600 mb-8">
                  We'll analyze your responses and send a personalized package recommendation.
                </p>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Business Name
                    </label>
                    <input
                      {...register('businessName')}
                      type="text"
                      className="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="Your business name"
                    />
                    {errors.businessName && (
                      <p className="text-red-500 text-sm mt-1">{errors.businessName.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Your Name
                    </label>
                    <input
                      {...register('yourName')}
                      type="text"
                      className="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="Your full name"
                    />
                    {errors.yourName && (
                      <p className="text-red-500 text-sm mt-1">{errors.yourName.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      WhatsApp Number
                    </label>
                    <input
                      {...register('whatsapp')}
                      type="tel"
                      className="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="+254 7XX XXX XXX"
                    />
                    {errors.whatsapp && (
                      <p className="text-red-500 text-sm mt-1">{errors.whatsapp.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <input
                      {...register('email')}
                      type="email"
                      className="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                    {errors.email && (
                      <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Navigation */}
            <div className="flex justify-between items-center mt-8">
              <button
                type="button"
                onClick={prevStep}
                disabled={currentStep === 1}
                className={`flex items-center gap-2 px-6 py-3 rounded-xl font-medium transition-colors ${
                  currentStep === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-orange-500 hover:text-orange-600'
                }`}
              >
                <ChevronLeft className="w-5 h-5" />
                Back
              </button>

              {currentStep < 4 ? (
                <button
                  type="button"
                  onClick={nextStep}
                  className="flex items-center gap-2 bg-orange-500 text-white px-8 py-3 rounded-xl font-medium hover:bg-orange-600 transition-colors"
                >
                  Continue
                  <ChevronRight className="w-5 h-5" />
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-orange-500 text-white px-8 py-3 rounded-xl font-medium hover:bg-orange-600 transition-colors disabled:opacity-50"
                >
                  {isSubmitting ? 'Processing...' : 'Get My Custom Package'}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
    </section>
  );
}
