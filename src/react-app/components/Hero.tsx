export default function Hero() {
  const scrollToForm = () => {
    const element = document.getElementById('form');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const scrollToPackages = () => {
    const element = document.getElementById('packages');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="hero" className="relative min-h-screen flex items-center justify-center overflow-hidden" itemScope itemType="https://schema.org/Service">
      {/* Eagle Background Image */}
      <div 
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: `url('/images/hero-eagle-background.jpg')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center center',
          backgroundRepeat: 'no-repeat'
        }}
      />
      
      {/* Dark Overlay */}
      <div className="absolute inset-0 bg-black/40 z-10" />
      
      {/* Content */}
      <div className="relative z-20 max-w-4xl mx-auto px-6 lg:px-20 text-center">
        <h1 className="text-5xl lg:text-7xl font-bold text-white mb-6 leading-tight" itemProp="name">
          Business Growth,<br />
          <span className="text-orange-500">Predictable Results</span>
        </h1>
        
        <p className="text-xl lg:text-2xl text-white/90 mb-12 max-w-3xl mx-auto leading-relaxed" itemProp="description">
          Professional social media management and accounting services that help businesses worldwide scale systematically. Get organized, generate leads, and focus on what you do best.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-6 justify-center">
          <button
            onClick={scrollToPackages}
            className="bg-orange-500 text-white px-10 py-4 rounded-xl text-lg font-medium hover:bg-orange-600 transition-all duration-300 hover:scale-105"
          >
            See Our Packages
          </button>
          <button
            onClick={scrollToForm}
            className="border-2 border-white text-white px-10 py-4 rounded-xl text-lg font-medium hover:bg-white hover:text-gray-900 transition-all duration-300"
          >
            Book Free Consultation
          </button>
        </div>
      </div>
    </section>
  );
}
