import { Check, Star } from 'lucide-react';
import { sectionBackgroundStyle } from '@/shared/theme';

export default function Packages() {
  const packages = [
    {
      name: "Social Media Package",
      price: "KES 35,000",
      period: "/month",
      description: "Businesses wanting consistent online presence",
      features: [
        "12 professional posts per month (Facebook + Instagram)",
        "2 short videos/reels monthly",
        "Community management and response handling",
        "Monthly performance report with lead tracking",
        "Strategy consultation every month"
      ],
      bestFor: "Service businesses, retail, restaurants, personal brands",
      popular: false
    },
    {
      name: "Complete Business Package",
      price: "KES 45,000",
      period: "/month",
      description: "Serious businesses ready to scale systematically",
      originalPrice: "KES 50,000",
      savings: "Save KES 5,000",
      features: [
        "Everything from Social Media Package",
        "Everything from Accounting Package", 
        "Integrated reporting (marketing ROI + financial performance)",
        "Priority support and monthly strategy calls",
        "Quarterly business growth planning session"
      ],
      bestFor: "Growing businesses ready to professionalize operations",
      popular: true
    },
    {
      name: "Accounting Package",
      price: "KES 15,000",
      period: "/month", 
      description: "Businesses needing organized financial management",
      features: [
        "Complete monthly bookkeeping (up to 500 transactions)",
        "Monthly financial statements and cash flow report",
        "Quarterly tax preparation and KRA filing",
        "WhatsApp support for daily financial questions"
      ],
      bestFor: "Any business with regular transactions and tax obligations",
      popular: false
    }
  ];

  const scrollToForm = () => {
    const element = document.getElementById('form');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="packages" className="py-24" style={sectionBackgroundStyle}>
      <div className="max-w-6xl mx-auto px-6 lg:px-20">
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            Choose your growth package
          </h2>
          <p className="text-xl text-gray-600">
            Professional services at transparent pricing
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {packages.map((pkg, index) => (
            <div 
              key={index} 
              className={`relative bg-white rounded-2xl p-8 border-2 transition-all duration-300 hover:-translate-y-2 hover:shadow-xl ${
                pkg.popular ? 'border-orange-500 shadow-lg' : 'border-gray-200 shadow-md'
              }`}
            >
              {pkg.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-orange-500 text-white px-6 py-2 rounded-full flex items-center gap-2 text-sm font-medium">
                    <Star className="w-4 h-4" />
                    MOST POPULAR
                  </div>
                </div>
              )}

              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                <p className="text-gray-600 mb-4">{pkg.description}</p>
                
                <div className="flex items-baseline justify-center mb-2">
                  <span className="text-4xl font-bold text-gray-900">{pkg.price}</span>
                  <span className="text-gray-600 ml-1">{pkg.period}</span>
                </div>
                
                {pkg.originalPrice && (
                  <div className="text-center">
                    <span className="text-gray-400 line-through text-lg">{pkg.originalPrice}</span>
                    <div className="text-green-600 font-medium text-sm">{pkg.savings}</div>
                  </div>
                )}
              </div>

              <div className="space-y-4 mb-8">
                {pkg.features.map((feature, idx) => (
                  <div key={idx} className="flex items-start gap-3">
                    <Check className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700 text-sm">{feature}</span>
                  </div>
                ))}
              </div>

              <div className="mb-6">
                <div className="text-sm font-medium text-gray-900 mb-2">Best For:</div>
                <div className="text-sm text-gray-600">{pkg.bestFor}</div>
              </div>

              <button
                onClick={scrollToForm}
                className={`w-full py-3 px-6 rounded-xl font-medium transition-colors ${
                  pkg.popular
                    ? 'bg-orange-500 text-white hover:bg-orange-600'
                    : 'border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white'
                }`}
              >
                Get Started
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
