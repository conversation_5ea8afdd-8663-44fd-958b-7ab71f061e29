import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Quote } from 'lucide-react';
import { sectionBackgroundStyle } from '@/shared/theme';

export default function Testimonials() {
  const testimonials = [
    {
      quote: "MaoConsult helped us go from random social media posts to 15 qualified leads per month. The accounting service means I finally understand my numbers and track performance properly.",
      author: "<PERSON>",
      business: "Bloom Beauty Studio",
      results: "15 leads/month, 40% revenue growth",
      image: "/images/social2.jpg"
    },
    {
      quote: "Before MaoConsult, I was stressed about taxes and social media management. Now both services run smoothly while I focus on serving customers and growing the business effectively.",
      author: "<PERSON>", 
      business: "Kimani Electronics",
      results: "Zero tax stress, 25% more online visibility",
      image: "/images/account2.jpg"
    },
    {
      quote: "The integrated approach is genius for business growth. I can see exactly how much each social media campaign contributes to my bottom line and make data-driven decisions consistently.",
      author: "<PERSON>",
      business: "Grace Catering Services", 
      results: "Clear ROI tracking, 30% business growth",
      image: "/images/leads.jpg"
    }
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  // Auto-advance slideshow every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 10000);

    return () => clearInterval(interval);
  }, [testimonials.length]);

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1);
  };

  const currentTestimonial = testimonials[currentIndex];

  return (
    <section className="py-24" style={sectionBackgroundStyle}>
      <div className="max-w-6xl mx-auto px-24 lg:px-24">
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            Results our clients love
          </h2>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12">
          {/* Text Content Cell */}
          <div className="bg-white rounded-2xl p-8 lg:p-12 flex flex-col justify-center relative min-h-[400px]">
            {/* Quote Icon - positioned to extend outside */}
            <div className="absolute -top-8 left-8">
              <div className="w-16 h-16 bg-black rounded-2xl flex items-center justify-center">
                <Quote className="w-8 h-8 text-white fill-current" />
              </div>
            </div>

            {/* Navigation Arrows */}
            <div className="absolute top-4 right-4 flex gap-2">
              <button
                onClick={goToPrevious}
                className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
                aria-label="Previous testimonial"
              >
                <ChevronLeft className="w-4 h-4 text-gray-600" />
              </button>
              <button
                onClick={goToNext}
                className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
                aria-label="Next testimonial"
              >
                <ChevronRight className="w-4 h-4 text-gray-600" />
              </button>
            </div>

            {/* Testimonial Content */}
            <div className="mt-8 flex-1 flex flex-col justify-center">
              <blockquote className="text-xl lg:text-2xl font-medium text-gray-900 leading-relaxed mb-6">
                "{currentTestimonial.quote}"
              </blockquote>
              
              <div className="space-y-3">
                <div className="text-base lg:text-lg font-semibold text-gray-900">
                  {currentTestimonial.author} · {currentTestimonial.business}
                </div>
                <div className="bg-orange-50 rounded-lg p-3 inline-block">
                  <div className="text-orange-800 font-medium text-sm">
                    Results: {currentTestimonial.results}
                  </div>
                </div>
              </div>
            </div>

            {/* Progress Indicators */}
            <div className="flex gap-2 mt-6">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentIndex 
                      ? 'bg-orange-500 w-8' 
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>
          </div>

          {/* Image Cell */}
          <div className="bg-white rounded-2xl p-4 flex items-center justify-center min-h-[400px]">
            <div className="w-full h-[400px] rounded-xl overflow-hidden">
              <img 
                src={currentTestimonial.image} 
                alt={currentTestimonial.author}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
