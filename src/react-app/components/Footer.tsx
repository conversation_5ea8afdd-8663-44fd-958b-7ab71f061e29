import { Linkedin, Instagram, Facebook } from 'lucide-react';
import { sectionBackgroundStyle } from '@/shared/theme';

export default function Footer() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="py-12" style={sectionBackgroundStyle}>
      <div className="max-w-6xl mx-auto px-6 lg:px-20">
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          {/* Links */}
          <div className="flex flex-wrap items-center gap-8">
            <button
              onClick={() => scrollToSection('services')}
              className="text-gray-600 hover:text-orange-500 transition-colors text-sm"
            >
              Services
            </button>
            <button
              onClick={() => scrollToSection('packages')}
              className="text-gray-600 hover:text-orange-500 transition-colors text-sm"
            >
              Packages
            </button>
            <button
              onClick={() => scrollToSection('contact')}
              className="text-gray-600 hover:text-orange-500 transition-colors text-sm"
            >
              Contact
            </button>
            <a
              href="#"
              className="text-gray-600 hover:text-orange-500 transition-colors text-sm"
            >
              Privacy
            </a>
          </div>

          {/* Social Icons */}
          <div className="flex items-center gap-4">
            <a
              href="#"
              className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 hover:bg-orange-500 hover:text-white transition-colors"
            >
              <Linkedin className="w-5 h-5" />
            </a>
            <a
              href="#"
              className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 hover:bg-orange-500 hover:text-white transition-colors"
            >
              <Instagram className="w-5 h-5" />
            </a>
            <a
              href="#"
              className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 hover:bg-orange-500 hover:text-white transition-colors"
            >
              <Facebook className="w-5 h-5" />
            </a>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-200 mt-8 pt-8 text-center">
          <p className="text-gray-600 text-sm">
            © 2025 MaoConsult. Professional business services worldwide.
          </p>
        </div>
      </div>
    </footer>
  );
}
