import { sectionBackgroundStyle } from '@/shared/theme';

export default function CTA() {
  const scrollToForm = () => {
    const element = document.getElementById('form');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="contact" className="py-24" style={sectionBackgroundStyle}>
      <div className="max-w-4xl mx-auto px-6 lg:px-20 text-center">
        <h2 className="text-5xl font-bold text-white mb-6">
          Ready to grow your business?
        </h2>
        <p className="text-xl text-white/90 mb-12 max-w-2xl mx-auto leading-relaxed">
          Join 100+ businesses worldwide getting predictable results with professional marketing and accounting services.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-6 justify-center">
          <button
            onClick={scrollToForm}
            className="bg-white text-orange-500 px-10 py-4 rounded-xl text-lg font-medium hover:bg-gray-100 transition-all duration-300 hover:scale-105"
          >
            Get Started Today
          </button>
          <button
            onClick={scrollToForm}
            className="border-2 border-white text-white px-10 py-4 rounded-xl text-lg font-medium hover:bg-white hover:text-orange-500 transition-all duration-300"
          >
            Book Free 15-Min Call
          </button>
        </div>
      </div>
    </section>
  );
}
