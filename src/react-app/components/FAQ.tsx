import { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { sectionBackgroundStyle } from '@/shared/theme';

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: "How is this different from hiring freelancers?",
      answer: "You get consistent professional service, integrated reporting, and accountability. Freelancers often disappear or deliver inconsistent work. We're your reliable business partner."
    },
    {
      question: "What if I only need one service?",
      answer: "No problem! You can start with Social Media OR Accounting packages individually. Many clients upgrade to the Complete Package after seeing results."
    },
    {
      question: "How quickly will I see results?",
      answer: "Accounting organization shows immediate benefits. Social media typically generates measurable leads within 60-90 days of consistent posting and engagement."
    },
    {
      question: "Do you work with businesses globally?",
      answer: "Yes! We serve businesses worldwide. Most work is done digitally, with video calls for strategy sessions across all time zones."
    },
    {
      question: "What if I need to cancel?",
      answer: "30-day notice required. No long-term contracts or cancellation fees. We're confident you'll love the results."
    },
    {
      question: "How do I know this is worth the investment?",
      answer: "We provide monthly reports showing exactly what we delivered and the results generated. Most clients see ROI within 90 days."
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-24" style={sectionBackgroundStyle}>
      <div className="max-w-4xl mx-auto px-6 lg:px-20">
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            Common questions
          </h2>
        </div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div key={index} className="border border-gray-200 rounded-xl overflow-hidden">
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
              >
                <span className="text-lg font-semibold text-gray-900 pr-4">
                  {faq.question}
                </span>
                {openIndex === index ? (
                  <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                )}
              </button>
              
              {openIndex === index && (
                <div className="px-6 pb-6">
                  <p className="text-gray-600 leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
