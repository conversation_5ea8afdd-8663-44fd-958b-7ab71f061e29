{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "01990ba7-f454-732b-819a-c5aae6ddab4a",
  "main": "./src/worker/index.ts",
  "compatibility_date": "2025-06-17",
  "compatibility_flags": ["nodejs_compat"],
  "observability": {
    "enabled": true,
  },
  "upload_source_maps": true,
  "assets": {
    "not_found_handling": "single-page-application",
  },
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "01990ba7-f454-732b-819a-c5aae6ddab4a",
      "database_id": "01990ba7-f454-732b-819a-c5aae6ddab4a",
    },
  ],
}
