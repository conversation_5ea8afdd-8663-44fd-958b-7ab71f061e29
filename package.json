{"name": "maow<PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "dependencies": {"@hono/zod-validator": "^0.5.0", "@hookform/resolvers": "^5.2.1", "hono": "4.7.7", "lucide-react": "^0.510.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.62.0", "react-router": "^7.5.3", "zod": "^3.24.3"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.12.0", "@eslint/js": "9.25.1", "@types/node": "22.14.1", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@vitejs/plugin-react": "^5.0.2", "autoprefixer": "^10.4.21", "eslint": "9.25.1", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.19", "globals": "15.15.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "5.8.3", "typescript-eslint": "8.31.0", "vite": "^7.1.3", "wrangler": "^4.33.0"}, "scripts": {"build": "tsc -b && vite build", "cf-typegen": "wrangler types", "check": "tsc && vite build && wrangler deploy --dry-run", "dev": "vite", "lint": "eslint ."}}