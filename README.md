# Mao Consulting Website

Official website for Mao Consulting - A premier social media management agency specializing in digital marketing, content creation, and brand growth strategies for businesses across Kenya and beyond.

## 🚀 Deployment

This project is configured for **Netlify** deployment with the following setup:

- **Build Command**: `npm run build`
- **Publish Directory**: `dist`
- **Node Version**: 18

### Netlify Configuration

The project includes a `netlify.toml` file with:
- SPA redirect rules for React Router
- Security headers
- Asset caching optimization
- Build environment settings

## 🛠️ Development

### Prerequisites
- Node.js 18 or higher
- npm

### Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Build for production**:
   ```bash
   npm run build
   ```

4. **Run tests** (verify setup):
   ```bash
   ./test-setup.sh
   ```

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint

## 📁 Project Structure

```
src/
├── react-app/          # React application code
│   ├── components/     # Reusable components
│   ├── pages/         # Page components
│   └── main.tsx       # Application entry point
└── shared/            # Shared utilities and types
```

## 🌐 Deployment to Netlify

1. Push your code to a Git repository (GitHub, GitLab, etc.)
2. Connect your repository to Netlify
3. Netlify will automatically detect the `netlify.toml` configuration
4. Your site will be deployed and available at a `.netlify.app` domain

## 🔧 Migration Notes

This project was migrated from Cloudflare Workers to Netlify:
- Removed `@cloudflare/vite-plugin` and `wrangler` dependencies
- Removed Cloudflare-specific configuration files
- Added Netlify-specific configuration and optimizations
